{"name": "@tencent/aigcf-toolkits-trend", "private": true, "version": "0.0.0", "scripts": {"dev": "vite", "build": "vite build --mode prod", "build:loc": "vite build --mode loc", "build:test": "vite build --mode test", "build:pre": "vite build --mode pre", "build:prod": "vite build --mode prod", "format": "cross-env prettier ./src  --write", "preview": "vite preview"}, "dependencies": {"@tencent/aigcf-configs": "workspace:*", "@vueuse/core": "^10.3.0", "axios": "^1.4.0", "dayjs": "^1.11.10", "js-cookie": "^3.0.5", "pinia": "^2.1.6", "tdesign-icons-vue-next": "^0.2.2", "tdesign-vue-next": "^1.6.8", "vue": "^3.3.4", "vue-i18n": "9", "vue-router": "^4.2.4", "xlsx": "^0.18.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@vitejs/plugin-vue": "^4.2.3", "autoprefixer": "^10.4.14", "eslint-config-prettier": "^8.9.0", "eslint-plugin-vue": "^9.16.1", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "unplugin-auto-import": "^0.16.7", "unplugin-vue-components": "^0.25.2", "vite": "^4.4.5", "vue-tsc": "^1.8.5"}}