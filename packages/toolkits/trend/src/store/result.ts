import { ref, reactive, watch } from "vue";
import { defineStore } from "pinia";
import { Filter, ResultItem } from '@/types/result.ts'
import service from "@/utils/request.ts";
import dayjs from 'dayjs';

export const useResultStore = defineStore('result', () => {
  const filter = reactive<Filter>({
    taskId: '',
    dateRange: [],
    country: '',
    channel: '',
    language: '',
    task_name: '',
    creator: '',
  });
  const results = ref<ResultItem[]>([]);
  const loading = ref(false);
  const downloading = ref(false);

  const initFilter = (taskId: string | undefined) => {
    const nowDate = new Date();
    const preDate = new Date();

    preDate.setDate(preDate.getDate() - 6);
    filter.dateRange = [
      dayjs(preDate).format('YYYY-MM-DD'),
      dayjs(nowDate).format('YYYY-MM-DD'),
    ];

    if (taskId) {
      // 从具体某个任务跳转过来，去掉其他过滤条件
      filter.taskId = taskId;
      filter.country = '';
      filter.channel = '';
      filter.language = '';
    } else {
      filter.taskId = '';
      const filterStr = localStorage.getItem('filter');
      if (filterStr) {
        const storageFilter = JSON.parse(filterStr);
        if (storageFilter.channel === 0) {
          storageFilter.channel = '';
        }
        const keys = Object.keys(storageFilter) as Array<keyof Filter>;
        keys.forEach(<K extends keyof Filter>(key: K) => {
          filter[key] = storageFilter[key];
        });
      } else {
        filter.taskId = '';
        filter.country = 'US';
        filter.channel = '';
        filter.language = 'zh';
      }
      localStorage.setItem('filter', JSON.stringify({
        country: filter.country,
        channel: filter.channel,
        language: filter.language,
      }));
    }
  };

  const getResult = async () => {
    loading.value = true;
    const res = await service.post('/trend/result', {
      task_id: filter.taskId,
      start_time: filter.dateRange[0] ? dayjs(filter.dateRange[0]).unix() : undefined,
      end_time: filter.dateRange[1] ? dayjs(filter.dateRange[1] + ' 23:59:59').unix() : undefined,
      channels: filter.channel ? [filter.channel] : [],
      country: filter.country ? [filter.country] : [],
      language: filter.language,
      filter_task_name: filter.task_name,
      filter_username: filter.creator,
    }) as {
      code: number,
      data: {
        list: ResultItem[],
      }
    }
    loading.value = false;
    results.value = res.data.list;
    console.log(results.value);
  };

  const download = () => {
    console.log('下载数据');
  };

  watch(() => filter, () => {
    getResult();
    localStorage.setItem('filter', JSON.stringify({
      country: filter.country,
      channel: filter.channel,
      language: filter.language,
    }));
  }, {
    deep: true,
  })

  return {
    results,
    loading,
    downloading,
    filter,
    getResult,
    initFilter,
    download,
  };
});
